<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Student QR Codes - Nthanda</title>
        <link rel="stylesheet" href="styles.css">
        <script
            src="https://unpkg.com/qrcode@1.5.3/build/qrcode.min.js"></script>
        <style>
        body {
            background-color: var(--color-yellow);
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: var(--color-white);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: var(--color-blue);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            color: var(--color-blue);
            font-size: 18px;
            margin: 0;
        }
        
        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .qr-card {
            background-color: var(--color-white);
            border: 3px solid var(--color-blue);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .student-name {
            color: var(--color-blue);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .qr-code-container {
            margin: 15px 0;
        }
        
        .qr-code-container canvas {
            border: 2px solid var(--color-yellow);
            border-radius: 10px;
        }
        
        .student-info {
            color: var(--color-blue);
            font-size: 14px;
            margin-top: 10px;
        }
        
        .print-button {
            background-color: var(--color-green);
            color: var(--color-white);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 15px;
            transition: transform 0.2s;
        }
        
        .print-button:hover {
            transform: translateY(-2px);
        }
        
        .loading {
            text-align: center;
            color: var(--color-blue);
            font-size: 18px;
            margin: 50px 0;
        }
        
        .error {
            text-align: center;
            color: var(--color-red);
            font-size: 18px;
            margin: 50px 0;
        }
        
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border: none;
            }
            
            .print-button {
                display: none;
            }
        }
    </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Student QR Codes</h1>
                <p>Print these QR codes for students to use for quick login</p>
            </div>

            <div id="loading" class="loading">
                Loading student QR codes...
            </div>

            <div id="error" class="error" style="display: none;">
                Failed to load student data. Please try again.
            </div>

            <div id="qr-grid" class="qr-grid" style="display: none;">
                <!-- QR codes will be generated here -->
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="print-button" onclick="window.print()"
                    style="display: none;" id="print-all-btn">
                    Print All QR Codes
                </button>
            </div>
        </div>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for the QRCode library to load
            setTimeout(() => {
                loadStudentQRCodes();
            }, 500);
        });

        async function loadStudentQRCodes() {
            try {
                // Get all students with QR tokens from the database
                const response = await fetch('http://localhost:8000/api/students/qr-tokens');

                if (!response.ok) {
                    throw new Error('Failed to fetch student data');
                }

                const data = await response.json();
                generateQRCodes(data.students);

            } catch (error) {
                console.error('Error loading student QR codes:', error);

                // Fallback to mock data for demonstration
                const mockStudents = [
                    { id: 9, name: "Ethan Kumasewera", qr_token: "STU_L497muLQ1XUBhy3PkyOXwrUA70Yarbzu" },
                    { id: 10, name: "Wezzie Banda", qr_token: "STU_" + "b".repeat(32) },
                    { id: 11, name: "Mphatso Mlenga", qr_token: "STU_" + "c".repeat(32) },
                    { id: 12, name: "Wisdom Kumasewera", qr_token: "STU_" + "d".repeat(32) }
                ];

                setTimeout(() => {
                    generateQRCodes(mockStudents);
                }, 1000);
            }
        }

        function generateQRCodes(students) {
            const loadingEl = document.getElementById('loading');
            const errorEl = document.getElementById('error');
            const gridEl = document.getElementById('qr-grid');
            const printBtn = document.getElementById('print-all-btn');

            loadingEl.style.display = 'none';

            if (!students || students.length === 0) {
                errorEl.textContent = 'No students found.';
                errorEl.style.display = 'block';
                return;
            }

            gridEl.innerHTML = '';
            
            students.forEach(student => {
                const qrCard = createQRCard(student);
                gridEl.appendChild(qrCard);
            });

            gridEl.style.display = 'grid';
            printBtn.style.display = 'inline-block';
        }

        function createQRCard(student) {
            const card = document.createElement('div');
            card.className = 'qr-card';
            
            const studentName = document.createElement('div');
            studentName.className = 'student-name';
            studentName.textContent = student.name;
            
            const qrContainer = document.createElement('div');
            qrContainer.className = 'qr-code-container';
            
            const studentInfo = document.createElement('div');
            studentInfo.className = 'student-info';
            studentInfo.innerHTML = `Student ID: ${student.id}<br>Scan to login`;
            
            const printButton = document.createElement('button');
            printButton.className = 'print-button';
            printButton.textContent = 'Print This QR';
            printButton.onclick = () => printSingleQR(card);
            
            card.appendChild(studentName);
            card.appendChild(qrContainer);
            card.appendChild(studentInfo);
            card.appendChild(printButton);
            
            // Generate QR code
            if (typeof QRCode !== 'undefined') {
                QRCode.toCanvas(student.qr_token, {
                    width: 150,
                    height: 150,
                    margin: 2,
                    color: {
                        dark: '#1c407c',  // var(--color-blue)
                        light: '#ffffff'
                    }
                }, function(error, canvas) {
                    if (error) {
                        console.error('QR Code generation error:', error);
                        qrContainer.innerHTML = '<p style="color: red;">QR Code generation failed</p>';
                    } else {
                        qrContainer.appendChild(canvas);
                    }
                });
            } else {
                console.error('QRCode library not loaded');
                qrContainer.innerHTML = '<p style="color: red;">QR Code library not loaded</p>';
            }
            
            return card;
        }

        function printSingleQR(cardElement) {
            const printWindow = window.open('', '_blank');
            const cardClone = cardElement.cloneNode(true);
            
            // Remove print button from clone
            const printBtn = cardClone.querySelector('.print-button');
            if (printBtn) printBtn.remove();
            
            printWindow.document.write(`
                <html>
                <head>
                    <title>Student QR Code</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                        .qr-card { border: 2px solid #1c407c; border-radius: 15px; padding: 20px; display: inline-block; }
                        .student-name { font-size: 18px; font-weight: bold; color: #1c407c; margin-bottom: 15px; }
                        .student-info { color: #1c407c; margin-top: 10px; }
                        canvas { border: 2px solid #ffd93d; border-radius: 10px; }
                    </style>
                </head>
                <body>
                    ${cardClone.outerHTML}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
    </body>
</html>
