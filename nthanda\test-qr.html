<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Test QR Code - Nthanda</title>
        <link rel="stylesheet" href="styles.css">
        <script
            src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
        <style>
        body {
            background-color: var(--color-yellow);
            padding: 20px;
            text-align: center;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: var(--color-white);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: var(--color-blue);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            color: var(--color-blue);
            font-size: 18px;
            margin-bottom: 30px;
        }
        
        .qr-display {
            margin: 30px 0;
        }
        
        .qr-display canvas {
            border: 4px solid var(--color-blue);
            border-radius: 15px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .student-info {
            background-color: var(--color-yellow);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            color: var(--color-blue);
            font-weight: 600;
        }
        
        .instructions {
            color: var(--color-blue);
            font-size: 16px;
            line-height: 1.6;
            margin-top: 20px;
        }
        
        .test-button {
            background-color: var(--color-green);
            color: var(--color-white);
            border: none;
            border-radius: 15px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
    </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Test QR Code</h1>
                <p>Use this QR code to test the login functionality</p>
            </div>

            <div class="student-info">
                <strong>Student:</strong> Ethan Kumasewera<br>
                <strong>Student ID:</strong> 9<br>
                <strong>QR Token:</strong> STU_L497muLQ1XUBhy3PkyOXwrUA70Yarbzu
            </div>

            <div class="qr-display" id="qr-display">
                <!-- QR code will be generated here -->
            </div>

            <div class="instructions">
                <h3 style="color: var(--color-blue);">How to test:</h3>
                <ol style="text-align: left; max-width: 400px; margin: 0 auto;">
                    <li>Open the <a href="login.html" target="_blank"
                            style="color: var(--color-green);">login
                            page</a></li>
                    <li>Click on "QR Code" tab</li>
                    <li>Allow camera access when prompted</li>
                    <li>Point your camera at the QR code above</li>
                    <li>The login should happen automatically</li>
                </ol>
            </div>

            <div style="margin-top: 30px;">
                <button class="test-button"
                    onclick="window.open('login.html', '_blank')">
                    Open Login Page
                </button>
                <button class="test-button"
                    onclick="window.open('qr-codes.html', '_blank')">
                    View All QR Codes
                </button>
            </div>
        </div>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for the QRCode library to load
            setTimeout(() => {
                generateTestQR();
            }, 500);
        });

        function generateTestQR() {
            const qrToken = 'STU_L497muLQ1XUBhy3PkyOXwrUA70Yarbzu';
            const qrDisplay = document.getElementById('qr-display');

            if (typeof QRCode !== 'undefined') {
                QRCode.toCanvas(qrToken, {
                    width: 300,
                    height: 300,
                    margin: 3,
                    color: {
                        dark: '#1c407c',  // var(--color-blue)
                        light: '#ffffff'
                    }
                }, function(error, canvas) {
                    if (error) {
                        console.error('QR Code generation error:', error);
                        qrDisplay.innerHTML = '<p style="color: red;">QR Code generation failed</p>';
                    } else {
                        qrDisplay.appendChild(canvas);
                    }
                });
            } else {
                console.error('QRCode library not loaded');
                qrDisplay.innerHTML = '<p style="color: red;">QR Code library not loaded. Please refresh the page.</p>';
                // Try again after a delay
                setTimeout(generateTestQR, 1000);
            }
        }
    </script>
    </body>
</html>
