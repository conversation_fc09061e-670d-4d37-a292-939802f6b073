<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Lesson - Caring of the body</title>
        <link rel="stylesheet" href="styles.css">

        <style>
            :root {
                --color-blue: #1c407c;
                --color-yellow: #ffd93d;
                --color-dark-yellow: #e6c235;
                --color-white: #ffffff;
                --color-red: #ff5252;
                --color-green: #4caf50;
                --color-dark-green: #388e3c;
                --color-orange: #FF9800;
                --color-purple: #9C27B0;
                --color-pink: #EC407A;
                --color-teal: #26A69A;
                --color-lime: #C0CA33;
                --color-gray: #F5F5F5;
                --color-text-gray: #666;
            }
            
            body {
                background-color: var(--color-blue);
                font-family: "Fredoka", sans-serif;
                font-size: 16px;
                color: black;
                
            }
            
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 0;
                position: relative;
            }
            
            .header {
                padding: 16px;
                background-color: var(--color-blue);
                margin-bottom: 12px;
            }
            
            .header-top {
                display: flex;
                align-items: center;
                margin-bottom: 16px;
            }
            
            .back-button {
                margin-right: 16px;
                background-color: var(--color-yellow);
                width: 40px;
                height: 40px;
                border-radius: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            
            .back-button img {
                width: 24px;
                height: 24px;
            }
            
           
            
            .title-container {
                display: flex;
                align-items: center;
                padding: 0 8px;
            }
            
            .lesson-icon {
                margin-right: 12px;
                color: var(--color-orange);
                font-size: 32px;
            }
            
            .title {
                color: var(--color-white);
                font-weight: 700;
                font-size: 24px;
                margin: 0;
                display: inline-block;
            }
            
            
            
            .content {
                padding: 16px;
                border-top-left-radius: 24px;
                border-top-right-radius: 24px;
                background-color: var(--color-yellow);
            }
            
            .card {
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .text-card {
                padding: 16px;
                border-radius: 16px;
                background-color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .lesson-text {
                font-size: 14px;
                line-height: 20px;
                color: #333;
                font-weight: 400;
            }
            
            .separator {
            height: 1px;
            background-color: var(--color-gray-100);
            width: 100%;
            margin: 1.5rem 0;
        }

        .content-block-container-row {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 16px;
                padding: 16px;
            padding-bottom: 8px;
            padding-top: 8px;
                border-radius: 16px;
                background-color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 8px;
            border-left: 4px solid var(--color-lime);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .content-block-container-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        
        .content-block-container-column {
                display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 16px;
            padding-bottom: 8px;
            padding-top: 8px;
            border-radius: 16px;
            background-color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 8px;
            border-left: 4px solid var(--color-blue);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .content-block-container-column:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        
        .button {
            display: inline-flex;
                align-items: center;
                justify-content: center;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
                cursor: pointer;
            }
            
        .button-outline {
            background-color: transparent;
            border: 1px solid #e5e7eb;
            color: var(--color-gray-700);
            padding: 0.5rem 1rem;
        }
        
        .button-outline:hover {
            background-color: #f9fafb;
        }
        
        .button-icon {
            padding: 0.5rem;
        }
        
        
        
        .header-container {
            margin-bottom: 1.5rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .breadcrumb {
            font-size: 0.875rem;
            color: var(--color-gray-500);
            margin-bottom: 0.25rem;
        }
        
        .topic-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-gray-900);
        }
        
        .narration-button {
                display: flex;
                align-items: center;
                justify-content: center;
            border-radius: 9999px;
            background-color: var(--color-blue);
            padding: 12px;
            font-size: 14px;
            color: var(--color-white);
                cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: transform 0.2s ease, background-color 0.2s ease;
        }
        
        .narration-button:hover {
            transform: scale(1.05);
            background-color: var(--color-orange);
        }
        
        .narration-button svg {
            margin-right: 6px;
        }
        
        .content-block {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .content-block:last-child {
            border-bottom: none;
        }
        
        .heading-h1 {
            color: var(--color-blue);
            font-size: 28px;
            font-weight: 700;
            text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
            padding-bottom: 8px;
            border-bottom: 2px dashed var(--color-yellow);
        }
        
        .heading-h2 {
            color: var(--color-blue);
            font-size: 24px;
            font-weight: 700;
            text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
            padding-bottom: 6px;
            border-bottom: 2px dashed var(--color-yellow);
        }
        
        .heading-h3 {
            color: var(--color-blue);
            font-size: 20px;
                font-weight: 600;
            text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
        }
        
        .paragraph {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--color-text-gray);
        }
       
        
        .image {
            max-width: 100%;
            max-height: 24rem;
            object-fit: contain;
            margin: 0 auto;
            display: block;
            border-radius: 0.5rem;
        }
        
        .image-caption {
            margin-top: 0.5rem;
            text-align: center;
            font-size: 0.875rem;
            color: var(--color-gray-500);
        }
        
        .video-title {
            margin-bottom: 0.5rem;
            font-size: 1.125rem;
                font-weight: 500;
            }
            
        .video-container {
            padding: 10px;
                background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border: 2px solid var(--color-blue);
        }
        
        .video {
            width: 100%;
            border-radius: 0.5rem;
        }
        
        .iframe-container {
            position: relative;
            width: 100%;
            max-width: 42rem;
            margin: 0 auto;
            padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
            overflow: hidden;
            border-radius: 0.5rem;
        }
        
        .iframe-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 0;
        }
        
        .audio-title {
            margin-bottom: 0.5rem;
            font-size: 1.125rem;
            font-weight: 500;
        }
        
        .audio-player {
            width: 100%;
            max-width: 28rem;
        }
        
        .list-container {
            margin-bottom: 1rem;
        }
        
        .numbered-list {
            list-style-type: none;
            counter-reset: my-counter;
            padding-left: 0;
        }
        
        .numbered-list .list-item {
            position: relative;
            margin-bottom: 12px;
            padding-left: 40px;
            counter-increment: my-counter;
        }
        
        .numbered-list .list-item::before {
            content: counter(my-counter);
            position: absolute;
            left: 0;
            top: -2px;
            width: 30px;
            height: 30px;
            background-color: var(--color-yellow);
            border-radius: 50%;
                color: var(--color-blue);
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        }
        
        .bullet-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .bullet-list .list-item {
            position: relative;
            margin-bottom: 12px;
            padding-left: 32px;
        }
        
        .bullet-list .list-item::before {
            content: "";
            position: absolute;
            left: 0;
            top: 4px;
            width: 20px;
            height: 20px;
            background-color: var(--color-orange);
            border-radius: 50%;
            box-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        }
        
        .list-item {
            margin-bottom: 10px;
            line-height: 1.5;
                font-size: 16px;
            color: var(--color-text-gray);
        }
        
        .narration-container {
            margin-top: 0.5rem;
        }
        
        .hidden {
            display: none;
        }
        
        .empty-message {
            padding: 3rem 0;
            text-align: center;
        }
        
        .empty-text {
            color: var(--color-gray-500);
            }
            
            .complete-button {
            margin-top: 2.5rem;

                width: 100%;

border-radius: 8px;

background-color: rgb(22,163,74);

padding-left: 1rem;

padding-right: 1rem;

padding-top: 0.5rem;

padding-bottom: 0.5rem;

font-weight: 700;

color: rgb(255,255,255);
            }

            /* WYSIWYG Rich Text Content Styles */
            .wysiwyg-content {
                font-family: "Fredoka", sans-serif;
                line-height: 1.6;
                color: var(--color-text-gray);
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            .wysiwyg-content h1 {
                color: var(--color-blue);
                font-size: 24px;
                font-weight: 700;
                margin: 16px 0 12px 0;
                text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
                border-bottom: 2px dashed var(--color-yellow);
                padding-bottom: 6px;
            }

            .wysiwyg-content h2 {
                color: var(--color-blue);
                font-size: 20px;
                font-weight: 700;
                margin: 14px 0 10px 0;
                text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
                border-bottom: 2px dashed var(--color-yellow);
                padding-bottom: 4px;
            }

            .wysiwyg-content h3 {
                color: var(--color-blue);
                font-size: 18px;
                font-weight: 600;
                margin: 12px 0 8px 0;
                text-shadow: 1px 1px 0 rgba(0,0,0,0.1);
            }

            .wysiwyg-content h4,
            .wysiwyg-content h5,
            .wysiwyg-content h6 {
                color: var(--color-blue);
                font-size: 16px;
                font-weight: 600;
                margin: 10px 0 6px 0;
            }

            .wysiwyg-content p {
                font-size: 16px;
                line-height: 1.6;
                color: var(--color-text-gray);
                margin: 8px 0;
            }

            .wysiwyg-content strong {
                font-weight: 700;
                color: var(--color-blue);
            }

            .wysiwyg-content em {
                font-style: italic;
                color: var(--color-text-gray);
            }

            .wysiwyg-content u {
                text-decoration: underline;
                text-decoration-color: var(--color-orange);
            }

            .wysiwyg-content ul {
                list-style: none;
                padding-left: 0;
                margin: 12px 0;
            }

            .wysiwyg-content ul li {
                position: relative;
                margin-bottom: 8px;
                padding-left: 28px;
                font-size: 16px;
                line-height: 1.5;
                color: var(--color-text-gray);
            }

            .wysiwyg-content ul li::before {
                content: "";
                position: absolute;
                left: 0;
                top: 6px;
                width: 16px;
                height: 16px;
                background-color: var(--color-orange);
                border-radius: 50%;
                box-shadow: 1px 1px 2px rgba(0,0,0,0.2);
            }

            .wysiwyg-content ol {
                list-style: none;
                counter-reset: wysiwyg-counter;
                padding-left: 0;
                margin: 12px 0;
            }

            .wysiwyg-content ol li {
                position: relative;
                margin-bottom: 8px;
                padding-left: 32px;
                font-size: 16px;
                line-height: 1.5;
                color: var(--color-text-gray);
                counter-increment: wysiwyg-counter;
            }

            .wysiwyg-content ol li::before {
                content: counter(wysiwyg-counter);
                position: absolute;
                left: 0;
                top: 0;
                width: 24px;
                height: 24px;
                background-color: var(--color-yellow);
                border-radius: 50%;
                color: var(--color-blue);
                font-weight: bold;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                box-shadow: 1px 1px 2px rgba(0,0,0,0.2);
            }

            .wysiwyg-content blockquote {
                background-color: rgba(156, 39, 176, 0.1);
                border-left: 4px solid var(--color-purple);
                margin: 12px 0;
                padding: 12px 16px;
                border-radius: 8px;
                font-style: italic;
                color: var(--color-text-gray);
            }

            .wysiwyg-content a {
                color: var(--color-blue);
                text-decoration: underline;
                text-decoration-color: var(--color-orange);
                font-weight: 500;
            }

            .wysiwyg-content a:hover {
                color: var(--color-orange);
                text-decoration-color: var(--color-blue);
            }

            .wysiwyg-content code {
                background-color: rgba(156, 39, 176, 0.1);
                color: var(--color-purple);
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                font-size: 14px;
            }

            .wysiwyg-content pre {
                background-color: var(--color-gray);
                color: var(--color-text-gray);
                padding: 12px;
                border-radius: 8px;
                overflow-x: auto;
                margin: 12px 0;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                line-height: 1.4;
            }

            .wysiwyg-content img {
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                margin: 8px 0;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            /* Mobile-specific adjustments */
            @media (max-width: 480px) {
                .wysiwyg-content h1 {
                    font-size: 20px;
                }

                .wysiwyg-content h2 {
                    font-size: 18px;
                }

                .wysiwyg-content h3 {
                    font-size: 16px;
                }

                .wysiwyg-content p,
                .wysiwyg-content li {
                    font-size: 15px;
                }

                .wysiwyg-content blockquote {
                    padding: 10px 12px;
                    margin: 10px 0;
                }

                .wysiwyg-content pre {
                    padding: 10px;
                    font-size: 13px;
                }
            }
        </style>
    </head>
    <body>
        <div class="stars-container" id="starsContainer"></div>
        <div class="container">
            <div class="header">
                <div class="header-top">
                    <div class="back-button" onclick="goBack()">
                        <img src="assets/icons/arrowleft.png" alt="Back">
                    </div>
                    <h1 class="title">
                        <span id="title">Topic Name</span>
                    </h1>
                </div>

            </div>

            <div class="content" id="content-blocks-container">

            </div>

        </div>
        <script src="jquery.js"></script>
        <script src="auth.js"></script>
        <script src="content.js"></script>
        <script src="axios.js"></script>
        <script src="lesson.js"></script>

        <script>
            // Create animated stars in the background
           
            
            $(document).ready(function() {
                var topicId = getUrlParams("topic_id");
                var topicName = getTopicName(topicId);
                $("#title").text(topicName);

              
                fetchLessons();
                
                async function fetchLessons() {
                    var response = await getLessons(topicId);
                    renderContentBlocks(response);
                }
            });
            
            function goBack() {
                window.history.back();
            }
            
            $("body").on("click", ".complete-button", function() {
                markAsComplete();
            }); 

            function markAsComplete() {
                var topicId = getUrlParams("topic_id");
                var studentId = getStudentDetails().id;
                axios.post("http://*************:8000/api/lesson/mark-as-complete", {
                    topic_id: topicId,
                    user_id: studentId
                })
                .then(function(response) {
                    goBack();
                })
                .catch(function(error) {
                    console.log(error);
                });
            }
        </script>
    </body>
</html>